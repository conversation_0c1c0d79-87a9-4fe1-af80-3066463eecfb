import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import Sidebar from './Sidebar';
import Header from './Header';
import PerformanceMetricsButton from '../ui/PerformanceMetricsButton';
import { useTheme } from '../../context/ThemeContext';
import { selectUser } from '../../redux/selectors/authSelectors';
import styled from 'styled-components';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: ${({ theme }) => theme.gradients.background};
  position: relative;
  overflow: hidden;
`;

const ContentWrapper = styled.div`
  display: flex;
  flex: 1;
  position: relative;
`;

const PageContent = styled.div`
  flex: 1;
  padding: 20px;
  padding-left: 280px; /* Add padding to account for sidebar width plus some margin */
  overflow-y: auto;
  height: calc(100vh - ${({ theme }) => theme.header.height});
  margin-top: ${({ theme }) => theme.header.height};
  
  @media (max-width: 768px) {
    padding-left: 20px;
  }
`;

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');
  const { theme } = useTheme();
  const user = useSelector(selectUser);

  const handleMenuSelect = (menuItem: string) => {
    setActiveMenuItem(menuItem);
  };

  return (
    <MainContainer>
      <Header
        userName={user?.name || 'User'}
        userRole={user?.role || 'User'}
      />
      <ContentWrapper>
        <Sidebar activeItem={activeMenuItem} onMenuSelect={handleMenuSelect} />
        <PageContent>
          {children}
        </PageContent>
      </ContentWrapper>
      <PerformanceMetricsButton />
    </MainContainer>
  );
};

export default MainLayout;