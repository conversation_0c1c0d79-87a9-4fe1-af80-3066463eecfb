import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../context/ThemeContext';
import reportWebVitals from '../../reportWebVitals';

interface PerformanceMetricsButtonProps {
  onClick?: () => void;
}

const FloatingButton = styled.button`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: ${props => props.theme.gradients.primaryButton || props.theme.colors.primary};
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s ease;
  color: white;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    background: ${props => props.theme.button?.primaryHover || props.theme.colors.primary};
  }
  
  &:active {
    transform: translateY(0);
  }
  
  @media (max-width: 768px) {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
  }
`;

const MetricsIcon = styled.svg`
  width: 24px;
  height: 24px;
  fill: currentColor;
  
  @media (max-width: 768px) {
    width: 20px;
    height: 20px;
  }
`;

const Tooltip = styled.div<{ visible: boolean }>`
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: ${props => props.theme.colors.cardBackground || '#333'};
  color: ${props => props.theme.colors.text || 'white'};
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: ${props => props.visible ? 1 : 0};
  visibility: ${props => props.visible ? 'visible' : 'hidden'};
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 16px;
    border: 4px solid transparent;
    border-top-color: ${props => props.theme.colors.cardBackground || '#333'};
  }
`;

const ModalOverlay = styled.div<{ visible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.visible ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
`;

const ModalContent = styled.div`
  background: ${props => props.theme.colors.cardBackground};
  border-radius: 12px;
  padding: 24px;
  min-width: 400px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;

  @media (max-width: 768px) {
    min-width: 320px;
    margin: 20px;
    max-height: calc(100vh - 40px);
  }
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ModalTitle = styled.h2`
  font-size: 20px;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${props => props.theme.colors.text};
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: ${props => props.theme.colors.border};
  }
`;

const ModalBody = styled.div`
  color: ${props => props.theme.colors.text};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
`;

const MetricCard = styled.div`
  background: ${props => props.theme.colors.background};
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid ${props => props.theme.colors.border};
`;

const MetricValue = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: ${props => props.theme.colors.primary};
  margin-bottom: 4px;
`;

const MetricLabel = styled.div`
  font-size: 12px;
  color: ${props => props.theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

interface WebVitalsData {
  CLS?: number;
  FID?: number;
  FCP?: number;
  LCP?: number;
  TTFB?: number;
}

const PerformanceMetricsButton: React.FC<PerformanceMetricsButtonProps> = ({ onClick }) => {
  const { theme } = useTheme();
  const [showTooltip, setShowTooltip] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [webVitalsData, setWebVitalsData] = useState<WebVitalsData>({});

  useEffect(() => {
    // Start collecting web vitals data when component mounts
    reportWebVitals((metric) => {
      setWebVitalsData(prev => ({
        ...prev,
        [metric.name]: metric.value
      }));
    });
  }, []);

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      setShowModal(true);
    }
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const formatMetricValue = (value: number | undefined, unit: string = 'ms') => {
    if (value === undefined) return 'N/A';
    if (unit === 'ms') {
      return value < 1000 ? `${Math.round(value)}ms` : `${(value / 1000).toFixed(1)}s`;
    }
    return `${value.toFixed(3)}`;
  };

  return (
    <>
      <FloatingButton
        onClick={handleClick}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        aria-label="Performance Metrics"
      >
        <Tooltip visible={showTooltip}>
          Performance Metrics
        </Tooltip>
        <MetricsIcon viewBox="0 0 24 24">
          <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9zm4-3h2v12h-2V9z"/>
        </MetricsIcon>
      </FloatingButton>

      {/* Performance Monitor Modal */}
      <ModalOverlay visible={showModal} onClick={closeModal}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <ModalHeader>
            <ModalTitle>
              🚀 Performance Monitor
            </ModalTitle>
            <CloseButton onClick={closeModal}>
              ×
            </CloseButton>
          </ModalHeader>

          <ModalBody>
            <MetricsGrid>
              <MetricCard>
                <MetricValue>{formatMetricValue(webVitalsData.LCP)}</MetricValue>
                <MetricLabel>LCP (Largest Contentful Paint)</MetricLabel>
              </MetricCard>
              <MetricCard>
                <MetricValue>{formatMetricValue(webVitalsData.FID)}</MetricValue>
                <MetricLabel>FID (First Input Delay)</MetricLabel>
              </MetricCard>
              <MetricCard>
                <MetricValue>{formatMetricValue(webVitalsData.CLS, '')}</MetricValue>
                <MetricLabel>CLS (Cumulative Layout Shift)</MetricLabel>
              </MetricCard>
              <MetricCard>
                <MetricValue>{formatMetricValue(webVitalsData.FCP)}</MetricValue>
                <MetricLabel>FCP (First Contentful Paint)</MetricLabel>
              </MetricCard>
              <MetricCard>
                <MetricValue>{formatMetricValue(webVitalsData.TTFB)}</MetricValue>
                <MetricLabel>TTFB (Time to First Byte)</MetricLabel>
              </MetricCard>
            </MetricsGrid>

            <div style={{ marginTop: '16px', fontSize: '14px' }}>
              <p>Real-time Web Vitals performance metrics for your application.</p>
              <p style={{ marginTop: '8px', color: theme.colors.textSecondary }}>
                Last updated: {new Date().toLocaleTimeString()}
              </p>
              <p style={{ marginTop: '4px', fontSize: '12px', color: theme.colors.textSecondary }}>
                Metrics are collected using Google's web-vitals library
              </p>
            </div>
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    </>
  );
};

export default PerformanceMetricsButton;
