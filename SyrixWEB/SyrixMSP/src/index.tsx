import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import store from './redux/store';
import App from './App';
import reportWebVitals from './reportWebVitals';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();

// CSS override to move performance monitor overlay from top-right to bottom-right
if (process.env.NODE_ENV === 'development') {
  const style = document.createElement('style');
  style.textContent = `
    /* Move performance monitor overlay to bottom-right */
    [data-testid*="performance"],
    [class*="performance-monitor"],
    [class*="Performance"],
    [id*="performance"],
    [id*="Performance"],
    div[style*="position: fixed"][style*="top"][style*="right"],
    div[style*="position:fixed"][style*="top"][style*="right"] {
      top: auto !important;
      bottom: 20px !important;
      right: 20px !important;
    }
  `;
  document.head.appendChild(style);
}
